# COMPREHENSIVE REQUIREMENTS DOCUMENT
## Waymarker - Journey Tracking Mobile Application

### 1. APPLICATION OVERVIEW

**App Name:** Waymarker
**Platform:** React Native (Expo)
**Primary Purpose:** Personal journey tracking and milestone management application that allows users to document, visualize, and analyze their progress across various life journeys.

**Core Value Proposition:** Enable users to track meaningful life journeys through waypoints (moments/milestones), visualize progress over time, and gain insights into their personal growth patterns.

### 2. TECHNICAL ARCHITECTURE

**State Management:** Zustand with persistence
**Navigation:** React Navigation (Stack + Tab navigation)
**Styling:** Consistent design system with soft pastels and blue (#2196F3) as primary color
**Data Storage:** Local storage with AsyncStorage, prepared for future cloud sync
**Icons:** Lucide React Native icons throughout

**File Structure Requirements:**
- `mocks/` - Sample journey and waypoint data
- `constants/colors.ts` - Color palette and theme constants
- `components/ui/` - Reusable UI components
- `components/` - Feature-specific components
- `store/` - Zustand stores for state management
- `screens/` - All screen components
- `navigation/` - Navigation configuration
- `App.tsx` - Root application component

### 3. DATA MODELS & TYPES

**Journey Type:**
```typescript
interface Journey {
  id: string;
  title: string;
  description?: string;
  color: string; // Hex color code
  startDate: Date;
  createdAt: Date;
  updatedAt: Date;
  waypointCount: number;
  milestoneCount: number;
}
```

**Waypoint Type:**
```typescript
interface Waypoint {
  id: string;
  journeyId: string;
  content: string; // Max 280 characters
  date: Date;
  sentiment: number; // -100 to 100
  impact: 'low' | 'medium' | 'high';
  isMilestone: boolean;
  createdAt: Date;
}
```

**Color Palette:**
- Primary: #2196F3 (Blue)
- Success: #10B981 (Green)
- Warning: #F59E0B (Amber)
- Error: #EF4444 (Red)
- Purple: #8B5CF6
- Orange: #F97316
- Teal: #14B8A6
- Pink: #EC4899
- Indigo: #6366F1
- Gray variants: #F9FAFB, #F3F4F6, #E5E7EB, #9CA3AF, #6B7280, #374151

### 4. SCREEN SPECIFICATIONS

#### 4.1 Journey List Screen (Home)
**Purpose:** Main dashboard showing all user journeys
**Layout:** Vertical scrolling list with floating action button

**Components Required:**
- Header with app title "waymarker." and search/filter icons
- Journey cards displaying:
  - Colored circle indicator (journey color)
  - Journey title
  - Waypoint count with flag icon
  - Milestone count with star icon
  - Last updated timestamp
- Floating action button (+) for creating new journey
- Empty state when no journeys exist

**Interactions:**
- Tap journey card → Navigate to Journey Timeline
- Tap search icon → Open search functionality
- Tap filter icon → Open filter options
- Tap FAB → Navigate to Create Journey modal

#### 4.2 Create Journey Screen
**Purpose:** Modal for creating new journeys
**Layout:** Form with validation and color selection

**Form Fields:**
- Title (required, text input)
- Description (optional, multiline text)
- Start Date (date picker, defaults to current month/year)
- Journey Color (grid of 10 color options, blue selected by default)

**Components Required:**
- Modal header with close button and "Create Journey" title
- Form validation (title required)
- Color selection grid (5x2 layout)
- Create button (disabled until valid)

**Interactions:**
- Color selection updates button color
- Form validation prevents submission
- Success creates journey and navigates back

#### 4.3 Journey Timeline Screen
**Purpose:** Chronological view of waypoints for a specific journey
**Layout:** Header with tabs, scrolling timeline content

**Header Components:**
- Back navigation
- Journey color indicator
- Journey title
- Waypoint count and start date
- Search, filter, and menu icons

**Tab Navigation:**
- Timeline (active)
- Path
- Insights

**Timeline Content:**
- Waypoint cards with:
  - Date stamp
  - Content text
  - Sentiment indicators (lightning bolts)
  - Impact slider visualization
  - Milestone badge (if applicable)
  - Special styling for milestones (gradient background)

**Interactions:**
- Tab switching between Timeline/Path/Insights
- Waypoint card taps for editing
- FAB for adding new waypoint

#### 4.4 Journey Path Screen
**Purpose:** Visual path representation of journey progress
**Layout:** Interactive path visualization with playback controls

**Components Required:**
- Same header as Timeline
- Path visualization area with:
  - Timeline bar at top
  - Waypoints plotted by sentiment (vertical position)
  - Selected waypoint highlighting
  - Milestone indicators (larger circles with glow)
- Playback controls:
  - Progress slider
  - Previous/Next buttons
  - Play/Pause button
  - Skip controls
- Selected waypoint detail card at bottom

**Interactions:**
- Waypoint selection updates detail card
- Playback controls animate through timeline
- Path visualization responds to user interaction

#### 4.5 Journey Insights Screen
**Purpose:** Analytics and statistics for journey progress
**Layout:** Dashboard with metrics and visualizations

**Metrics Grid (2x2):**
- Total waypoints count
- Milestones count (with star icon)
- Positive sentiment percentage
- Days active count

**Visualizations:**
- Sentiment distribution (donut chart)
  - Positive (blue): 87%
  - Neutral (gray): 8%
  - Challenge (red): 5%
- Monthly progress bar chart
- Insights card with trend analysis

**Components Required:**
- Metric cards with large numbers and labels
- Interactive donut chart
- Bar chart with month labels
- Insight card with icon and descriptive text

#### 4.6 Add Waypoint Screen
**Purpose:** Modal for creating new waypoints
**Layout:** Form with sentiment and impact controls

**Form Components:**
- Content textarea (280 character limit with counter)
- Date picker (defaults to current date)
- Sentiment selector:
  - 9-dot scale from negative to positive
  - Plus/minus buttons for adjustment
  - Dynamic label showing sentiment level
- Impact selector (Low/Medium/High with visual indicators)
- Milestone toggle (star icon)

**Interactions:**
- Character counter updates in real-time
- Sentiment selection updates label and dot sizes
- Impact selection shows visual feedback
- Milestone toggle changes appearance

#### 4.7 Settings Screen
**Purpose:** App configuration and user preferences
**Layout:** Grouped settings sections

**Settings Sections:**
- Account (sign-in options)
- Appearance (theme selection)
- Notifications (settings link)
- Support (help, privacy, terms)
- Data & Storage (management options)
- About (version, website link)

**Components Required:**
- Section headers
- Setting rows with icons and labels
- Theme toggle buttons
- External link indicators
- Version information

### 5. NAVIGATION STRUCTURE

**Navigation Type:** Stack Navigator with Modal presentations

**Route Structure:**
```
App
├── JourneyList (Home)
├── CreateJourney (Modal)
├── JourneyDetail
│   ├── Timeline (Tab)
│   ├── Path (Tab)
│   └── Insights (Tab)
├── AddWaypoint (Modal)
└── Settings
```

**Navigation Rules:**
- Journey List is the root screen
- Create Journey and Add Waypoint are modal presentations
- Journey Detail uses tab navigation for Timeline/Path/Insights
- Settings accessible from Journey List header
- Back navigation preserves context

### 6. STATE MANAGEMENT REQUIREMENTS

#### 6.1 Journey Store
**Purpose:** Manage journey data and operations

**State Properties:**
- `journeys: Journey[]`
- `selectedJourney: Journey | null`
- `loading: boolean`
- `error: string | null`

**Actions Required:**
- `createJourney(journey: Omit<Journey, 'id' | 'createdAt' | 'updatedAt'>)`
- `updateJourney(id: string, updates: Partial<Journey>)`
- `deleteJourney(id: string)`
- `selectJourney(id: string)`
- `getJourneyById(id: string)`

#### 6.2 Waypoint Store
**Purpose:** Manage waypoint data and operations

**State Properties:**
- `waypoints: Waypoint[]`
- `loading: boolean`
- `error: string | null`

**Actions Required:**
- `createWaypoint(waypoint: Omit<Waypoint, 'id' | 'createdAt'>)`
- `updateWaypoint(id: string, updates: Partial<Waypoint>)`
- `deleteWaypoint(id: string)`
- `getWaypointsByJourney(journeyId: string)`
- `getMilestonesByJourney(journeyId: string)`

#### 6.3 App Store
**Purpose:** Global app state and preferences

**State Properties:**
- `theme: 'light' | 'dark' | 'system'`
- `isFirstLaunch: boolean`
- `lastActiveJourney: string | null`

**Actions Required:**
- `setTheme(theme: string)`
- `setFirstLaunch(value: boolean)`
- `setLastActiveJourney(journeyId: string)`

### 7. COMPONENT SPECIFICATIONS

#### 7.1 UI Components

**JourneyCard Component:**
- Props: journey, onPress, showStats
- Displays journey info with color indicator
- Handles press interactions
- Shows waypoint/milestone counts

**WaypointCard Component:**
- Props: waypoint, journey, onPress, isMilestone
- Conditional styling for milestones
- Sentiment visualization
- Impact indicator

**SentimentSelector Component:**
- Props: value, onChange, disabled
- 9-dot interactive scale
- Plus/minus controls
- Dynamic labeling

**ImpactSelector Component:**
- Props: value, onChange, disabled
- Three-level visual selector
- Size-based indication
- Selection feedback

**ColorPicker Component:**
- Props: selectedColor, onColorSelect, colors
- Grid layout (5x2)
- Selection indication
- Color validation

#### 7.2 Screen Components

**Header Component:**
- Props: title, showBack, rightActions, color
- Consistent styling across screens
- Dynamic action buttons
- Color theming support

**TabBar Component:**
- Props: tabs, activeTab, onTabChange
- Three-tab layout for journey detail
- Icon and text labels
- Active state indication

### 8. MOCK DATA REQUIREMENTS

**Sample Journeys:**
1. "Career Transition" (Blue) - 12 waypoints, 3 milestones
2. "Fitness Journey" (Green) - 28 waypoints, 5 milestones
3. "Learning Spanish" (Purple) - 18 waypoints, 2 milestones
4. "Home Renovation" (Orange) - 7 waypoints, 1 milestone

**Sample Waypoints:**
- Mix of positive, neutral, and challenging sentiments
- Various impact levels
- Realistic content (150-280 characters)
- Distributed dates over 6-month period
- Include milestone examples

### 9. STYLING GUIDELINES

**Design Principles:**
- Light, minimalistic aesthetic
- Soft pastels with blue primary
- Consistent spacing (4px grid system)
- Rounded corners (8px, 12px, 16px)
- Subtle shadows and borders
- Smooth animations (150-300ms)

**Typography:**
- Headers: 18-24px, semibold
- Body: 14-16px, regular
- Captions: 12px, medium
- Color hierarchy: gray-900, gray-700, gray-500

**Interactive Elements:**
- Touch feedback (opacity 0.7)
- Scale animations for cards
- Color transitions for selections
- Loading states for async operations

### 10. IMPLEMENTATION PRIORITIES

**Phase 1 (Core Functionality):**
1. Basic navigation structure
2. Journey creation and listing
3. Simple waypoint addition
4. Local data persistence

**Phase 2 (Enhanced Features):**
1. Timeline visualization
2. Sentiment and impact tracking
3. Search and filtering
4. Settings and preferences

**Phase 3 (Advanced Features):**
1. Path visualization
2. Insights and analytics
3. Data export capabilities
4. Performance optimizations

### 11. ACCESSIBILITY REQUIREMENTS

- Screen reader support for all interactive elements
- Sufficient color contrast ratios
- Touch target sizes (minimum 44px)
- Keyboard navigation support
- Semantic HTML structure
- Alternative text for icons

### 12. PERFORMANCE CONSIDERATIONS

- Lazy loading for large journey lists
- Optimized re-renders with proper memoization
- Efficient date calculations
- Image optimization for any future media features
- Bundle size optimization

### 13. FUTURE EXTENSIBILITY

**Prepared Architecture For:**
- Cloud synchronization
- User authentication
- Journey sharing
- Media attachments
- Export functionality
- Notification system
- Collaborative journeys

This requirements document provides comprehensive specifications for building a production-ready Waymarker application that delivers immediate value to users while maintaining clean architecture and extensibility for future enhancements.

Please start building the entire app in sequence and make sure to look at REQUIREMENT.md (Do not overwrite it) and the other relevant markdown file operations to ensure you are on right track. Remember, the screenshots are only available for a few messages.