import React from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import { Minus, Plus } from 'lucide-react-native';
import { COLORS } from '../../constants/colors';

interface SentimentSelectorProps {
  value: number; // -100 to 100
  onChange: (newValue: number) => void;
  journeyColor: string;
}

export default function SentimentSelector({ value, onChange, journeyColor }: SentimentSelectorProps) {
  // Convert sentiment value to dot index (0-8)
  const getSelectedDotIndex = () => {
    // Map -100 to 100 range to 0-8 range
    const normalized = (value + 100) / 200; // 0 to 1
    return Math.round(normalized * 8); // 0 to 8
  };

  // Convert dot index to sentiment value
  const getDotValue = (index: number) => {
    // Map 0-8 range to -100 to 100 range
    return (index / 8) * 200 - 100;
  };

  const selectedIndex = getSelectedDotIndex();

  const getSentimentLabel = (value: number) => {
    if (value >= 60) return 'Very Positive';
    if (value >= 20) return 'Positive';
    if (value > -20) return 'Neutral';
    if (value > -60) return 'Challenging';
    return 'Very Challenging';
  };

  const renderDots = () => {
    const dots = [];
    for (let i = 0; i < 9; i++) {
      const isSelected = i === selectedIndex;
      const dotValue = getDotValue(i);
      
      dots.push(
        <Pressable
          key={i}
          style={[
            styles.dot,
            isSelected && styles.selectedDot,
            {
              backgroundColor: isSelected ? journeyColor : COLORS.gray400
            }
          ]}
          onPress={() => onChange(dotValue)}
          hitSlop={{ top: 10, bottom: 10, left: 4, right: 4 }}
        />
      );
    }
    return dots;
  };

  return (
    <View style={styles.wrapper}>
      <View style={styles.container}>
        <Minus size={20} color={COLORS.gray500} strokeWidth={2.5} />
        
        <View style={styles.dotsContainer}>
          <View style={styles.line} />
          {renderDots()}
        </View>
        
        <Plus size={20} color={COLORS.gray500} strokeWidth={2.5} />
      </View>
      
      <Text style={styles.label}>
        {getSentimentLabel(value)}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    alignItems: 'center',
    gap: 12
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12
  },
  dotsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 8,
    position: 'relative'
  },
  line: {
    position: 'absolute',
    height: 2,
    backgroundColor: '#E5E7EB',
    width: '100%',
    top: '50%',
    transform: [{ translateY: -1 }],
    zIndex: 0
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    zIndex: 1
  },
  selectedDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    zIndex: 1
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.gray700,
    textAlign: 'center',
    marginTop: 4
  }
});