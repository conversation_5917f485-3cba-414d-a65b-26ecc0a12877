import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity
} from 'react-native';
import { COLORS } from '../../constants/colors';
import SentimentSelector from '../form/SentimentSelector';
import ImpactPicker from '../form/ImpactPicker';
import MilestoneToggle from '../form/MilestoneToggle';
import DatePicker from '../form/DatePicker';
import { Waypoint } from '../../mocks/journeys';

interface WaypointFormProps {
  initialData?: Partial<Waypoint>;
  onSubmit: (data: {
    content: string;
    date: Date;
    sentiment: number;
    impact: 'low' | 'medium' | 'high';
    isMilestone: boolean;
  }) => void;
  buttonText?: string;
  journeyColor: string;
}

export default function WaypointForm({
  initialData,
  onSubmit,
  buttonText = 'Add Waypoint',
  journeyColor
}: WaypointFormProps) {
  const [content, setContent] = useState(initialData?.content || '');
  const [date, setDate] = useState(initialData?.date || new Date());
  const [sentiment, setSentiment] = useState(initialData?.sentiment || 0);
  const [impact, setImpact] = useState<'low' | 'medium' | 'high'>(initialData?.impact || 'medium');
  const [isMilestone, setIsMilestone] = useState(initialData?.isMilestone || false);
  
  const isValid = content.trim().length > 0;
  const characterCount = content.length;
  const maxCharacters = 280;

  // Update form when initialData changes (for editing)
  useEffect(() => {
    if (initialData) {
      setContent(initialData.content || '');
      setDate(initialData.date || new Date());
      setSentiment(initialData.sentiment || 0);
      setImpact(initialData.impact || 'medium');
      setIsMilestone(initialData.isMilestone || false);
    }
  }, [initialData]);

  const handleSubmit = () => {
    if (!isValid) return;

    onSubmit({
      content: content.trim(),
      date,
      sentiment,
      impact,
      isMilestone
    });
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.contentInput}
              placeholder="What happened?"
              value={content}
              onChangeText={setContent}
              multiline
              textAlignVertical="top"
              maxLength={maxCharacters}
              placeholderTextColor={COLORS.gray400}
            />
            <Text style={styles.characterCount}>
              {characterCount}/{maxCharacters}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <DatePicker
            value={date}
            onChange={setDate}
            journeyColor={journeyColor}
          />
        </View>

        <View style={styles.mainFormContainer}>
          <View style={styles.section}>
            <Text style={styles.label}>How did this feel?</Text>
            <View style={styles.sentimentContainer}>
              <SentimentSelector
                value={sentiment}
                onChange={setSentiment}
                journeyColor={journeyColor}
              />
            </View>
          </View>

          <View style={styles.divider} />

          <View style={styles.formRow}>
            <View style={styles.impactSection}>
              <Text style={styles.label}>Impact</Text>
              <ImpactPicker
                value={impact}
                onChange={setImpact}
                journeyColor={journeyColor}
              />
            </View>

            <View style={styles.milestoneSection}>
              <Text style={styles.label}>Milestone</Text>
              <MilestoneToggle
                value={isMilestone}
                onChange={setIsMilestone}
                journeyColor={journeyColor}
              />
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            { backgroundColor: journeyColor },
            !isValid && styles.disabledButton
          ]}
          onPress={handleSubmit}
          disabled={!isValid}
        >
          <Text style={styles.submitButtonText}>{buttonText}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background
  },
  content: {
    flex: 1,
    padding: 24
  },
  section: {
    marginBottom: 32
  },
  inputContainer: {
    position: 'relative'
  },
  contentInput: {
    borderWidth: 1,
    borderColor: COLORS.gray200,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: COLORS.gray900,
    backgroundColor: COLORS.background,
    height: 120,
    textAlignVertical: 'top'
  },
  characterCount: {
    position: 'absolute',
    bottom: 12,
    right: 16,
    fontSize: 12,
    color: COLORS.gray400
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.gray900,
    marginBottom: 16
  },
  mainFormContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.gray200,
    padding: 20,
    marginBottom: 100
  },
  sentimentContainer: {
    marginBottom: 8
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.gray200,
    marginVertical: 8
  },
  formRow: {
    flexDirection: 'row',
    gap: 48,
    alignItems: 'flex-start',
    justifyContent: 'space-between'
  },
  impactSection: {
    flex: 1,
    alignItems: 'flex-start'
  },
  milestoneSection: {
    flex: 1,
    alignItems: 'flex-end'
  },
  footer: {
    padding: 24,
    paddingBottom: 40
  },
  submitButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center'
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600'
  },
  disabledButton: {
    backgroundColor: COLORS.gray400
  }
});