
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Pressable,
  Alert
} from 'react-native';
import { X, Check, Calendar } from 'lucide-react-native';
import { useJourneyStore } from '../stores/journeyStore';
import { COLORS, JOURNEY_COLORS } from '../constants/colors';
import { formatDisplayDate } from '../utils/dateUtils';

interface CreateJourneyScreenProps {
  navigation: any;
}

export default function CreateJourneyScreen({ navigation, route }: CreateJourneyScreenProps) {
  const journeyId = route.params?.journeyId;
  const isEditing = !!journeyId;
  
  const getJourneyById = useJourneyStore(state => state.getJourneyById);
  const existingJourney = isEditing ? getJourneyById(journeyId) : null;
  
  const [title, setTitle] = useState(existingJourney?.title || '');
  const [description, setDescription] = useState(existingJourney?.description || '');
  const [selectedColor, setSelectedColor] = useState(existingJourney?.color || COLORS.primary);
  const [status, setStatus] = useState<'active' | 'done'>(existingJourney?.status || 'active');
  const [startDate] = useState(existingJourney?.startDate || new Date());
  
  const createJourney = useJourneyStore(state => state.createJourney);
  const updateJourney = useJourneyStore(state => state.updateJourney);
  
  const isValid = title.trim().length > 0;

  const handleSubmit = () => {
    if (!isValid) return;

    if (isEditing && journeyId) {
      updateJourney(journeyId, {
        title: title.trim(),
        description: description.trim() || undefined,
        color: selectedColor,
        status
      });
    } else {
      createJourney({
        title: title.trim(),
        description: description.trim() || undefined,
        color: selectedColor,
        startDate,
        status: 'active'
      });
    }

    navigation.goBack();
  };



  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Pressable 
          style={styles.closeButton}
          onPress={() => navigation.goBack()}
        >
          <X size={24} color={COLORS.gray700} />
        </Pressable>
        <Text style={styles.headerTitle}>{isEditing ? 'Edit Journey' : 'Create Journey'}</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.label}>
            Title <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={styles.input}
            placeholder="Enter journey title"
            value={title}
            onChangeText={setTitle}
            placeholderTextColor={COLORS.gray400}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Describe your journey..."
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
            placeholderTextColor={COLORS.gray400}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Start Date</Text>
          <View style={styles.dateInput}>
            <Text style={styles.dateText}>{formatDisplayDate(startDate)}</Text>
            <Calendar size={20} color={COLORS.gray400} />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Journey Color</Text>
          <View style={styles.colorGrid}>
            {JOURNEY_COLORS.map((color, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.colorOption,
                  { backgroundColor: color },
                  selectedColor === color && styles.selectedColor
                ]}
                onPress={() => setSelectedColor(color)}
              >
                {selectedColor === color && (
                  <Check size={20} color="#FFFFFF" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {isEditing && (
          <View style={styles.section}>
            <Text style={styles.label}>Status</Text>
            <View style={styles.statusContainer}>
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  status === 'active' && styles.selectedStatus
                ]}
                onPress={() => setStatus('active')}
              >
                <Text style={[
                  styles.statusText,
                  status === 'active' && styles.selectedStatusText
                ]}>Active</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  status === 'done' && styles.selectedStatus
                ]}
                onPress={() => setStatus('done')}
              >
                <Text style={[
                  styles.statusText,
                  status === 'done' && styles.selectedStatusText
                ]}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.createButton,
            { backgroundColor: selectedColor },
            !isValid && styles.disabledButton
          ]}
          onPress={handleSubmit}
          disabled={!isValid}
        >
          <Check size={20} color="#FFFFFF" />
          <Text style={styles.createButtonText}>{isEditing ? 'Save Changes' : 'Create Journey'}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200
  },
  closeButton: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.gray900
  },
  placeholder: {
    width: 40
  },
  content: {
    flex: 1,
    padding: 24
  },
  section: {
    marginBottom: 24
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.gray900,
    marginBottom: 8
  },
  required: {
    color: COLORS.error
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.gray200,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: COLORS.gray900,
    backgroundColor: COLORS.background
  },
  textArea: {
    height: 100
  },
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLORS.gray200,
    borderRadius: 12,
    padding: 16,
    backgroundColor: COLORS.background
  },
  dateText: {
    fontSize: 16,
    color: COLORS.gray900
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16
  },
  colorOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8
  },
  selectedColor: {
    borderWidth: 3,
    borderColor: 'rgba(0,0,0,0.1)'
  },
  statusContainer: {
    flexDirection: 'row',
    gap: 12
  },
  statusOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.gray200,
    backgroundColor: COLORS.background,
    alignItems: 'center'
  },
  selectedStatus: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.gray700
  },
  selectedStatusText: {
    color: '#FFFFFF'
  },
  footer: {
    padding: 24,
    paddingBottom: 40
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600'
  },
  disabledButton: {
    backgroundColor: COLORS.gray400
  }
});
