import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  TouchableWithoutFeedback,
  Platform,
  Dimensions
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { COLORS } from '../constants/colors';

export interface DropdownOption {
  label: string;
  icon?: React.ReactNode;
  onPress?: () => void;
  destructive?: boolean;
}

interface DropdownMenuProps {
  visible: boolean;
  onClose: () => void;
  options: DropdownOption[];
  position: { x: number; y: number };
}

const DropdownMenu = ({ visible, onClose, options, position }: DropdownMenuProps) => {
  const animation = useRef(new Animated.Value(0)).current;
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
  
  useEffect(() => {
    if (visible) {
      Animated.spring(animation, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 30,
      }).start();
    } else {
      Animated.timing(animation, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);
  
  const handleOptionPress = (option: DropdownOption) => {
    // Haptic feedback
    if (option.destructive) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    } else {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    onClose();
    option.onPress && option.onPress();
  };
  
  if (!visible) return null;
  
  // Calculate menu dimensions
  const menuWidth = 200;
  const menuHeight = options.length * 48 + 16; // 48px per option + padding
  
  // Position the menu near the trigger but ensure it stays on screen
  let menuX = position.x - menuWidth + 20; // Offset from right edge of trigger
  let menuY = position.y + 10; // Slightly below trigger
  
  // Keep menu on screen
  if (menuX < 10) menuX = 10;
  if (menuX + menuWidth > screenWidth - 10) menuX = screenWidth - menuWidth - 10;
  if (menuY + menuHeight > screenHeight - 50) menuY = position.y - menuHeight - 10;
  
  const menuStyle = {
    opacity: animation,
    transform: [
      {
        scale: animation.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1],
        }),
      },
      {
        translateY: animation.interpolate({
          inputRange: [0, 1],
          outputRange: [-10, 0],
        }),
      },
    ],
  };
  
  return (
    <View style={StyleSheet.absoluteFill}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.backdrop} />
      </TouchableWithoutFeedback>
      
      <Animated.View
        style={[
          styles.menu,
          {
            left: menuX,
            top: menuY,
            width: menuWidth,
          },
          menuStyle,
        ]}
      >
        {options.map((option, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.menuItem,
              index === options.length - 1 && styles.lastMenuItem,
            ]}
            onPress={() => handleOptionPress(option)}
            activeOpacity={0.7}
          >
            <View style={styles.menuItemContent}>
              {option.icon && (
                <View style={styles.iconContainer}>
                  {option.icon}
                </View>
              )}
              <Text
                style={[
                  styles.menuItemText,
                  option.destructive && styles.destructiveText,
                ]}
              >
                {option.label}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  menu: {
    position: 'absolute',
    backgroundColor: COLORS.background,
    borderRadius: 12,
    paddingVertical: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  menuItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: COLORS.gray200,
  },
  lastMenuItem: {
    borderBottomWidth: 0,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 20,
    height: 20,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: 16,
    color: COLORS.gray900,
    fontWeight: '500',
  },
  destructiveText: {
    color: COLORS.error,
  },
});

export default DropdownMenu;