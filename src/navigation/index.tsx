
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import JourneyListScreen from '../screens/JourneyListScreen';
import CreateJourneyScreen from '../screens/CreateJourneyScreen';
import JourneyDetailScreen from '../screens/JourneyDetailScreen';
import AddWaypointScreen from '../screens/AddWaypointScreen';
import SettingsScreen from '../screens/SettingsScreen';

export type RootStackParamList = {
  JourneyList: undefined;
  CreateJourney: { journeyId?: string };
  JourneyDetail: { journeyId: string };
  AddWaypoint: { journeyId: string };
  Settings: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();



export default function MainNavigator() {
  return (
    <Stack.Navigator
      initialRouteName="JourneyList"
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right'
      }}
    >
      <Stack.Screen name="JourneyList" component={JourneyListScreen} />
      <Stack.Screen 
        name="CreateJourney" 
        component={CreateJourneyScreen}
        options={{
          presentation: 'modal',
          animation: 'slide_from_bottom'
        }}
      />
      <Stack.Screen name="JourneyDetail" component={JourneyDetailScreen} />
      <Stack.Screen 
        name="AddWaypoint" 
        component={AddWaypointScreen}
        options={{
          presentation: 'modal',
          animation: 'slide_from_bottom'
        }}
      />
      <Stack.Screen name="Settings" component={SettingsScreen} />
    </Stack.Navigator>
  );
}
