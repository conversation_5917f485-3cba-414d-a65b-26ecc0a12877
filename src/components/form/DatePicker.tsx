import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Modal,
  ScrollView,
  Platform
} from 'react-native';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react-native';
import { COLORS } from '../../constants/colors';
import { formatDatePicker } from '../../utils/dateUtils';

interface DatePickerProps {
  value: Date;
  onChange: (newDate: Date) => void;
  journeyColor: string;
  placeholder?: string;
}

export default function DatePicker({ value, onChange, journeyColor, placeholder }: DatePickerProps) {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const today = new Date();
  const [selectedYear, setSelectedYear] = useState(today.getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(today.getMonth());
  const [selectedDay, setSelectedDay] = useState(today.getDate());

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];



  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const handleDateSelect = () => {
    const newDate = new Date(selectedYear, selectedMonth, selectedDay);
    onChange(newDate);
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    // Reset to current value with safety checks
    const safeValue = value instanceof Date && !isNaN(value.getTime()) ? value : new Date();
    setSelectedYear(safeValue.getFullYear());
    setSelectedMonth(safeValue.getMonth());
    setSelectedDay(safeValue.getDate());
    setIsModalVisible(false);
  };

  const renderCalendar = () => {
    const daysInMonth = getDaysInMonth(selectedYear, selectedMonth);
    const firstDay = getFirstDayOfMonth(selectedYear, selectedMonth);
    const days = [];
    const today = new Date();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<View key={`empty-${i}`} style={styles.emptyDay} />);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const currentDate = new Date(selectedYear, selectedMonth, day);
      const isFuture = currentDate > today;
      const isSelected = day === selectedDay;
      const isDisabled = isFuture;
      
      days.push(
        <Pressable
          key={day}
          style={[
            styles.dayButton,
            isSelected && !isDisabled && { backgroundColor: journeyColor },
            isDisabled && styles.disabledDay
          ]}
          onPress={() => !isDisabled && setSelectedDay(day)}
          disabled={isDisabled}
        >
          <Text
            style={[
              styles.dayText,
              isSelected && !isDisabled && { color: '#FFFFFF' },
              isDisabled && { color: COLORS.gray300 }
            ]}
          >
            {day}
          </Text>
        </Pressable>
      );
    }

    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      if (selectedMonth === 0) {
        setSelectedMonth(11);
        setSelectedYear(selectedYear - 1);
      } else {
        setSelectedMonth(selectedMonth - 1);
      }
    } else {
      if (selectedMonth === 11) {
        setSelectedMonth(0);
        setSelectedYear(selectedYear + 1);
      } else {
        setSelectedMonth(selectedMonth + 1);
      }
    }
    
    // Adjust day if it doesn't exist in the new month
    const daysInNewMonth = getDaysInMonth(
      direction === 'prev' && selectedMonth === 0 ? selectedYear - 1 : 
      direction === 'next' && selectedMonth === 11 ? selectedYear + 1 : selectedYear,
      direction === 'prev' && selectedMonth === 0 ? 11 : 
      direction === 'next' && selectedMonth === 11 ? 0 : 
      direction === 'prev' ? selectedMonth - 1 : selectedMonth + 1
    );
    
    if (selectedDay > daysInNewMonth) {
      setSelectedDay(daysInNewMonth);
    }
  };

  return (
    <>
      <Pressable
        style={styles.dateInput}
        onPress={() => setIsModalVisible(true)}
      >
        <Text style={styles.dateText}>
          {placeholder || formatDatePicker(value)}
        </Text>
        <Calendar size={20} color={COLORS.gray400} />
      </Pressable>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="fade"
        onRequestClose={handleCancel}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Date</Text>
            </View>

            <View style={styles.calendarHeader}>
              <Pressable
                style={styles.navButton}
                onPress={() => navigateMonth('prev')}
              >
                <ChevronLeft size={20} color={COLORS.gray700} />
              </Pressable>
              
              <Text style={styles.monthYearText}>
                {months[selectedMonth]} {selectedYear}
              </Text>
              
              <Pressable
                style={styles.navButton}
                onPress={() => navigateMonth('next')}
              >
                <ChevronRight size={20} color={COLORS.gray700} />
              </Pressable>
            </View>

            <View style={styles.weekDaysHeader}>
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <Text key={day} style={styles.weekDayText}>
                  {day}
                </Text>
              ))}
            </View>

            <View style={styles.calendar}>
              {renderCalendar()}
            </View>

            <View style={styles.modalActions}>
              <Pressable style={styles.cancelButton} onPress={handleCancel}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </Pressable>
              
              <Pressable
                style={[styles.selectButton, { backgroundColor: journeyColor }]}
                onPress={handleDateSelect}
              >
                <Text style={styles.selectButtonText}>Select</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLORS.gray200,
    borderRadius: 12,
    padding: 16,
    backgroundColor: COLORS.background
  },
  dateText: {
    fontSize: 16,
    color: COLORS.gray900
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  modalContent: {
    backgroundColor: COLORS.background,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 350,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 20
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.gray900
  },
  calendarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16
  },
  navButton: {
    padding: 8,
    borderRadius: 8
  },
  monthYearText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.gray900
  },
  weekDaysHeader: {
    flexDirection: 'row',
    marginBottom: 8
  },
  weekDayText: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.gray500,
    paddingVertical: 8
  },
  calendar: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20
  },
  emptyDay: {
    width: '14.28%',
    height: 40
  },
  dayButton: {
    width: '14.28%',
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8
  },
  dayText: {
    fontSize: 14,
    color: COLORS.gray900
  },
  disabledDay: {
    backgroundColor: COLORS.gray100,
    opacity: 0.5
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12
  },
  cancelButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.gray200,
    alignItems: 'center'
  },
  cancelButtonText: {
    fontSize: 16,
    color: COLORS.gray700
  },
  selectButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center'
  },
  selectButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  }
});