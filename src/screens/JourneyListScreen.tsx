
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Pressable
} from 'react-native';
import { Search, Filter, Settings, Flag, Star, Plus } from 'lucide-react-native';
import { useJourneyStore } from '../stores/journeyStore';
import { useWaypointStore } from '../stores/waypointStore';
import { COLORS } from '../constants/colors';
import { formatTimeAgo } from '../utils/dateUtils';

interface JourneyListScreenProps {
  navigation: any;
}

export default function JourneyListScreen({ navigation }: JourneyListScreenProps) {
  const journeys = useJourneyStore(state => state.journeys);
  const waypoints = useWaypointStore(state => state.waypoints);



  const renderJourneyCard = (journey: any) => {
    // Calculate entry and milestone counts for this journey
    const journeyWaypoints = waypoints.filter(waypoint => waypoint.journeyId === journey.id);
    const entryCount = journeyWaypoints.length;
    const milestoneCount = journeyWaypoints.filter(waypoint => waypoint.isMilestone).length;

    return (
      <Pressable
        key={journey.id}
        style={styles.journeyCard}
        onPress={() => navigation.navigate('JourneyDetail', { journeyId: journey.id })}
      >
        <View style={styles.cardContent}>
          <View style={styles.journeyInfo}>
            <View style={[styles.colorCircle, { backgroundColor: journey.color }]} />
            <Text style={styles.journeyTitle}>{journey.title}</Text>
          </View>
          
          <View style={styles.metadataRow}>
            <View style={styles.metricsGroup}>
              <View style={[styles.metricItem, { marginRight: 12 }]}>
                <Flag size={14} color="#6B7280" style={{ marginRight: 4 }} />
                <Text style={styles.metadataText}>{entryCount}</Text>
              </View>
              <View style={styles.metricItem}>
                <Star size={14} color="#6B7280" style={{ marginRight: 4 }} />
                <Text style={styles.metadataText}>{milestoneCount}</Text>
              </View>
            </View>
            <Text style={styles.metadataText}>{formatTimeAgo(journey.updatedAt)}</Text>
          </View>
        </View>
      </Pressable>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>waymarker.</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => {
              // Handle search
            }}
          >
            <Search size={20} color={COLORS.gray700} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => {
              // Handle filter
            }}
          >
            <Filter size={20} color={COLORS.gray700} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Settings')}
          >
            <Settings size={20} color={COLORS.gray700} />
          </TouchableOpacity>
        </View>
      </View>

      {journeys.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyTitle}>Start Your Journey</Text>
          <Text style={styles.emptySubtitle}>
            Create your first journey and begin tracking meaningful moments and milestones.
          </Text>
          <TouchableOpacity
            style={styles.emptyButton}
            onPress={() => navigation.navigate('CreateJourney')}
          >
            <Plus size={20} color="#FFFFFF" />
            <Text style={styles.emptyButtonText}>Create Journey</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.journeyList}
          showsVerticalScrollIndicator={false}
        >
          {journeys.map(renderJourneyCard)}
        </ScrollView>
      )}

      <TouchableOpacity
        style={styles.fab}
        onPress={() => navigation.navigate('CreateJourney')}
      >
        <Plus size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: COLORS.gray900
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8
  },
  actionButton: {
    padding: 8,
    borderRadius: 8
  },
  scrollView: {
    flex: 1
  },
  journeyList: {
    padding: 20,
    paddingBottom: 100
  },
  journeyCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: COLORS.gray200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2
  },
  cardContent: {
    gap: 12
  },

  journeyInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1
  },
  colorCircle: {
    width: 16,
    height: 16,
    borderRadius: 8
  },
  journeyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.gray900,
    flex: 1
  },

  journeyStats: {
    flexDirection: 'row',
    gap: 16
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6
  },
  statText: {
    fontSize: 14,
    color: COLORS.gray700,
    fontWeight: '500'
  },
  metadataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8
  },
  metricsGroup: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  metadataText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280'
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: COLORS.gray900,
    marginBottom: 8
  },
  emptySubtitle: {
    fontSize: 16,
    color: COLORS.gray500,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: COLORS.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12
  },
  emptyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600'
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8
  }
});
