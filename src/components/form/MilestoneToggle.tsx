import React from 'react';
import { StyleSheet, Pressable, View, Text } from 'react-native';
import { Star } from 'lucide-react-native';
import { COLORS } from '../../constants/colors';

interface MilestoneToggleProps {
  value: boolean;
  onChange: (newValue: boolean) => void;
  journeyColor: string;
}

export default function MilestoneToggle({ value, onChange, journeyColor }: MilestoneToggleProps) {
  return (
    <Pressable
      style={styles.container}
      onPress={() => onChange(!value)}
      hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
    >
      <View
        style={[
          styles.circle,
          {
            backgroundColor: value ? journeyColor : 'transparent',
            borderColor: value ? journeyColor : COLORS.gray200,
            borderWidth: 2
          }
        ]}
      >
        <Star
          size={24}
          color={value ? '#FFFFFF' : COLORS.gray400}
          fill={value ? '#FFFFFF' : 'none'}
        />
      </View>

    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    height: 48,
    justifyContent: 'center'
  },
  circle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center'
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.gray700
  }
});