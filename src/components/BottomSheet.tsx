import React, { useEffect, useRef } from 'react';
import {
  View,
  Modal,
  Animated,
  Dimensions,
  Pressable,
  StyleSheet,
  PanResponder,
  Platform
} from 'react-native';
import { COLORS } from '../constants/colors';

interface BottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  height?: number;
}

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function BottomSheet({
  isVisible,
  onClose,
  children,
  height = 300
}: BottomSheetProps) {
  const translateY = useRef(new Animated.Value(height)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isVisible) {
      // Slide up and fade in backdrop
      Animated.parallel([
        Animated.spring(translateY, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0.5,
          duration: 250,
          useNativeDriver: true
        })
      ]).start();
    } else {
      // Slide down and fade out backdrop
      Animated.parallel([
        Animated.spring(translateY, {
          toValue: height,
          useNativeDriver: true,
          tension: 100,
          friction: 8
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true
        })
      ]).start();
    }
  }, [isVisible, height, translateY, backdropOpacity]);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return Math.abs(gestureState.dy) > 10;
      },
      onPanResponderMove: (evt, gestureState) => {
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        // Close if dragged down more than 100px or fast swipe down
        if (gestureState.dy > 100 || gestureState.vy > 1) {
          onClose();
        } else {
          // Snap back to open position
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
            tension: 100,
            friction: 8
          }).start();
        }
      }
    })
  ).current;

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="none"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      {/* Backdrop */}
      <Pressable style={styles.backdrop} onPress={onClose}>
        <Animated.View
          style={[
            styles.backdropOverlay,
            { opacity: backdropOpacity }
          ]}
        />
      </Pressable>

      {/* Bottom Sheet */}
      <Animated.View
        style={[
          styles.bottomSheet,
          {
            height,
            transform: [{ translateY }]
          }
        ]}
        {...panResponder.panHandlers}
      >
        {/* Drag Handle - Always visible */}
        <View style={styles.handleBar} />
        
        {/* Content */}
        <View style={styles.content}>
          {children}
        </View>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    justifyContent: 'flex-end',
    zIndex: 1000, // Ensure action sheet appears above FAB
  },
  backdropOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000000'
  },
  bottomSheet: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: Platform.OS === 'ios' ? 20 : 16,
    borderTopRightRadius: Platform.OS === 'ios' ? 20 : 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
      },
      android: {
        elevation: 16,
      },
    }),
  },
  handleBar: {
    width: 36,
    height: 5,
    backgroundColor: COLORS.gray300,
    borderRadius: 3,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 12
  },
  content: {
    flex: 1,
    paddingHorizontal: 0 // Remove padding - let ActionSheet handle it
  }
});