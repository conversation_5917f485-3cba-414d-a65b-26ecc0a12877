/**
 * Universal date utility functions for safe date handling
 */

/**
 * Safely converts any value to a Date object
 * @param value - Date, string, number, or undefined
 * @returns Valid Date object or current date as fallback
 */
export const ensureDate = (value: any): Date => {
  // If already a valid Date object
  if (value instanceof Date && !isNaN(value.getTime())) {
    return value;
  }
  
  // If it's a string or number, try to convert
  if (value && (typeof value === 'string' || typeof value === 'number')) {
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      return date;
    }
  }
  
  // Fallback to current date for any invalid input
  return new Date();
};

/**
 * Safely formats time ago with comprehensive error handling
 * @param date - Date object, string, or undefined
 * @returns Formatted time ago string
 */
export const formatTimeAgo = (date: any): string => {
  try {
    const validDate = ensureDate(date);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - validDate.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Updated now';
    if (diffInHours < 24) return `Updated ${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) return 'Updated 1d ago';
    if (diffInDays < 7) return `Updated ${diffInDays}d ago`;
    
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `Updated ${diffInWeeks}w ago`;
  } catch (error) {
    return 'Updated recently';
  }
};

/**
 * Safely formats date with comprehensive error handling
 * @param date - Date object, string, or undefined
 * @param fallback - Fallback text for invalid dates
 * @returns Formatted date string
 */
export const formatDate = (date: any, fallback: string = 'No date'): string => {
  try {
    const validDate = ensureDate(date);
    return validDate.toLocaleDateString('en-US', { 
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  } catch (error) {
    return fallback;
  }
};

/**
 * Safely formats date for display with fallback
 * @param date - Date object, string, or undefined
 * @returns Formatted date string or fallback
 */
export const formatDisplayDate = (date: any): string => {
  return formatDate(date, 'Select date');
};

/**
 * Safely formats date in MM/DD/YYYY format for date pickers
 * @param date - Date object, string, or undefined
 * @returns Formatted date string in MM/DD/YYYY format
 */
export const formatDatePicker = (date: any): string => {
  try {
    const validDate = ensureDate(date);
    return validDate.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    });
  } catch (error) {
    return 'Select date';
  }
};