
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants';

interface AppState {
  theme: string; // 'light' | 'dark' | 'system'
  isFirstLaunch: boolean;
  lastActiveJourney: string | null;
}

interface AppActions {
  setTheme: (theme: string) => void;
  setFirstLaunch: (value: boolean) => void;
  setLastActiveJourney: (journeyId: string) => void;
}

type AppStore = AppState & AppActions;

export const useAppStore = create<AppStore>()(persist(
  (set) => ({
    // Initial state
    theme: 'light',
    isFirstLaunch: true,
    lastActiveJourney: null,

    // Actions
    setTheme: (theme) => set({ theme }),
    
    setFirstLaunch: (value) => set({ isFirstLaunch: value }),
    
    setLastActiveJourney: (journeyId) => set({ lastActiveJourney: journeyId })
  }),
  {
    name: STORAGE_KEYS.APP_STORE,
    storage: {
      getItem: async (name: string) => {
        const value = await AsyncStorage.getItem(name);
        return value ? JSON.parse(value) : null;
      },
      setItem: async (name: string, value: any) => {
        await AsyncStorage.setItem(name, JSON.stringify(value));
      },
      removeItem: async (name: string) => {
        await AsyncStorage.removeItem(name);
      }
    }
  }
));
