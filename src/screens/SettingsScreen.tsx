
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Pressable
} from 'react-native';
import {
  ArrowLeft,
  ChevronRight,
  ExternalLink,
  User,
  Palette,
  Bell,
  HelpCircle,
  Shield,
  FileText,
  Database,
  Info,
  Globe
} from 'lucide-react-native';
import { useAppStore } from '../stores/appStore';
import { COLORS } from '../constants/colors';

interface SettingsScreenProps {
  navigation: any;
}

export default function SettingsScreen({ navigation }: SettingsScreenProps) {
  const { theme, setTheme } = useAppStore();

  const themeOptions = [
    { key: 'light', label: 'Light' },
    { key: 'dark', label: 'Dark' },
    { key: 'system', label: 'System' }
  ];

  const renderSectionHeader = (title: string) => (
    <Text style={styles.sectionHeader}>{title}</Text>
  );

  const renderSettingItem = (
    icon: any,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightElement?: React.ReactNode
  ) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingIcon}>
        {React.createElement(icon, { size: 20, color: COLORS.gray700 })}
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {rightElement || (onPress && (
        <ChevronRight size={16} color={COLORS.gray400} />
      ))}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color={COLORS.gray700} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSectionHeader('Account')}
        <View style={styles.section}>
          <Text style={styles.accountDescription}>
            Sign in to sync your journeys across devices and backup your data
          </Text>
          
          {renderSettingItem(
            User,
            'Continue with Google',
            undefined,
            () => {
              // Handle Google sign in
            }
          )}
          
          {renderSettingItem(
            User,
            'Continue with Email',
            undefined,
            () => {
              // Handle email sign in
            }
          )}
        </View>

        {renderSectionHeader('Appearance')}
        <View style={styles.section}>
          {renderSettingItem(
            Palette,
            'Theme',
            undefined,
            undefined,
            <View style={styles.themeSelector}>
              {themeOptions.map((option) => (
                <Pressable
                  key={option.key}
                  style={[
                    styles.themeOption,
                    theme === option.key && styles.selectedTheme
                  ]}
                  onPress={() => setTheme(option.key)}
                >
                  <Text
                    style={[
                      styles.themeText,
                      theme === option.key && styles.selectedThemeText
                    ]}
                  >
                    {option.label}
                  </Text>
                </Pressable>
              ))}
            </View>
          )}
        </View>

        {renderSectionHeader('Notifications')}
        <View style={styles.section}>
          {renderSettingItem(
            Bell,
            'Notification Settings',
            undefined,
            () => {
              // Handle notification settings
            }
          )}
        </View>

        {renderSectionHeader('Support')}
        <View style={styles.section}>
          {renderSettingItem(
            HelpCircle,
            'Help & FAQ',
            undefined,
            () => {
              // Handle help
            }
          )}
          
          {renderSettingItem(
            Shield,
            'Privacy Policy',
            undefined,
            () => {
              // Handle privacy policy
            }
          )}
          
          {renderSettingItem(
            FileText,
            'Terms of Service',
            undefined,
            () => {
              // Handle terms
            }
          )}
        </View>

        {renderSectionHeader('Data & Storage')}
        <View style={styles.section}>
          {renderSettingItem(
            Database,
            'Data Management',
            undefined,
            () => {
              // Handle data management
            }
          )}
        </View>

        {renderSectionHeader('About')}
        <View style={styles.section}>
          {renderSettingItem(
            Info,
            'Version',
            'v1.0.0',
            undefined
          )}
          
          {renderSettingItem(
            Globe,
            'waymarkerapp.com',
            undefined,
            () => {
              // Handle website link
            },
            <ExternalLink size={16} color={COLORS.gray400} />
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200
  },
  backButton: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.gray900
  },
  placeholder: {
    width: 40
  },
  content: {
    flex: 1,
    padding: 16
  },
  sectionHeader: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.gray900,
    marginTop: 24,
    marginBottom: 12,
    marginHorizontal: 4
  },
  section: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: COLORS.gray200
  },
  accountDescription: {
    fontSize: 14,
    color: COLORS.gray500,
    lineHeight: 20,
    padding: 16,
    paddingBottom: 0
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray100
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.gray100,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12
  },
  settingContent: {
    flex: 1
  },
  settingTitle: {
    fontSize: 16,
    color: COLORS.gray900,
    marginBottom: 2
  },
  settingSubtitle: {
    fontSize: 12,
    color: COLORS.gray500
  },
  themeSelector: {
    flexDirection: 'row',
    gap: 8
  },
  themeOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: COLORS.gray200
  },
  selectedTheme: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary
  },
  themeText: {
    fontSize: 12,
    color: COLORS.gray700
  },
  selectedThemeText: {
    color: '#FFFFFF'
  }
});
