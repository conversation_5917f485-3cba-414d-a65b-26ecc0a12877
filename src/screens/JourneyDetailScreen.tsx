
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Pressable,
  Modal
} from 'react-native';
import {
  ArrowLeft,
  Search,
  Filter,
  MoreHorizontal,
  MoreVertical,
  Flag,
  Star,
  Zap,
  Plus,
  TrendingUp,
  Play,
  SkipBack,
  SkipForward,
  ChevronLeft,
  ChevronRight,
  Edit,
  Trash2,
  X
} from 'lucide-react-native';
import { useJourneyStore } from '../stores/journeyStore';
import { useWaypointStore } from '../stores/waypointStore';
import { COLORS } from '../constants/colors';
import ConfirmationModal from '../components/modals/ConfirmationModal';
import WaypointForm from '../components/forms/WaypointForm';
import DropdownMenu, { DropdownOption } from '../components/ActionSheet';
import { formatDate } from '../utils/dateUtils';
import * as Haptics from 'expo-haptics';

interface JourneyDetailScreenProps {
  navigation: any;
  route: {
    params: {
      journeyId: string;
    };
  };
}

export default function JourneyDetailScreen({ navigation, route }: JourneyDetailScreenProps) {
  const { journeyId } = route.params;
  const [activeTab, setActiveTab] = useState<'timeline' | 'path' | 'insights'>('timeline');
  const [showJourneyMenu, setShowJourneyMenu] = useState(false);
  const [showWaypointMenu, setShowWaypointMenu] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const [showDeleteJourneyModal, setShowDeleteJourneyModal] = useState(false);
  const [showDeleteWaypointModal, setShowDeleteWaypointModal] = useState<string | null>(null);
  const [editingWaypoint, setEditingWaypoint] = useState<string | null>(null);
  
  const journey = useJourneyStore(state => state.getJourneyById(journeyId));
  const deleteJourney = useJourneyStore(state => state.deleteJourney);
  const updateJourney = useJourneyStore(state => state.updateJourney);
  const getWaypointsByJourney = useWaypointStore(state => state.getWaypointsByJourney);
  const updateWaypoint = useWaypointStore(state => state.updateWaypoint);
  const deleteWaypoint = useWaypointStore(state => state.deleteWaypoint);
  const waypoints = getWaypointsByJourney(journeyId);

  if (!journey) {
    navigation.goBack();
    return null;
  }

  const handleEditJourney = () => {
    navigation.navigate('CreateJourney', { journeyId });
  };

  const handleDeleteJourney = () => {
    setShowDeleteJourneyModal(true);
  };

  const confirmDeleteJourney = () => {
    deleteJourney(journeyId);
    setShowDeleteJourneyModal(false);
    navigation.goBack();
  };

  const handleEditWaypoint = (waypointId: string) => {
    setEditingWaypoint(waypointId);
  };

  const handleDeleteWaypoint = (waypointId: string) => {
    setShowDeleteWaypointModal(waypointId);
  };

  const confirmDeleteWaypoint = (waypointId: string) => {
    const waypoint = waypoints.find(w => w.id === waypointId);
    if (waypoint) {
      deleteWaypoint(waypointId);
      // Update journey counts
      updateJourney(journeyId, {
        waypointCount: journey.waypointCount - 1,
        milestoneCount: journey.milestoneCount - (waypoint.isMilestone ? 1 : 0),
        updatedAt: new Date()
      });
    }
    setShowDeleteWaypointModal(null);
  };

  const handleUpdateWaypoint = (data: {
    content: string;
    date: Date;
    sentiment: number;
    impact: 'low' | 'medium' | 'high';
    isMilestone: boolean;
  }) => {
    if (editingWaypoint) {
      const oldWaypoint = waypoints.find(w => w.id === editingWaypoint);
      updateWaypoint(editingWaypoint, data);
      
      // Update milestone count if changed
      if (oldWaypoint && oldWaypoint.isMilestone !== data.isMilestone) {
        updateJourney(journeyId, {
          milestoneCount: journey.milestoneCount + (data.isMilestone ? 1 : -1),
          updatedAt: new Date()
        });
      }
    }
    setEditingWaypoint(null);
  };

  // Journey Menu Options
  const journeyOptions: DropdownOption[] = [
    {
      label: 'Edit Journey',
      icon: <Edit size={20} color={COLORS.gray700} />,
      onPress: handleEditJourney
    },
    {
      label: 'Delete Journey',
      icon: <Trash2 size={20} color={COLORS.error} />,
      onPress: handleDeleteJourney,
      destructive: true
    }
  ];

  // Waypoint Menu Options
  const getWaypointOptions = (waypointId: string): DropdownOption[] => [
    {
      label: 'Edit Waypoint',
      icon: <Edit size={20} color={COLORS.gray700} />,
      onPress: () => handleEditWaypoint(waypointId)
    },
    {
      label: 'Delete Waypoint',
      icon: <Trash2 size={20} color={COLORS.error} />,
      onPress: () => handleDeleteWaypoint(waypointId),
      destructive: true
    }
  ];

  // Handle menu button press with position tracking
  const handleJourneyMenuPress = (event: any) => {
    const { pageX, pageY } = event.nativeEvent;
    setMenuPosition({ x: pageX, y: pageY });
    setShowJourneyMenu(true);
  };

  const handleWaypointMenuPress = (waypointId: string, event: any) => {
    const { pageX, pageY } = event.nativeEvent;
    setMenuPosition({ x: pageX, y: pageY });
    setShowWaypointMenu(waypointId);
  };



  const getSentimentBolts = (sentiment: number) => {
    const count = Math.ceil((Math.abs(sentiment) / 100) * 3);
    return Array(Math.max(1, count)).fill(0).map((_, i) => (
      <Zap
        key={i}
        size={12}
        color={sentiment >= 0 ? COLORS.primary : COLORS.gray400}
        fill={sentiment >= 0 ? COLORS.primary : 'none'}
      />
    ));
  };

  const getImpactBar = (impact: string) => {
    const width = impact === 'low' ? '33%' : impact === 'medium' ? '66%' : '100%';
    return (
      <View style={styles.impactBarContainer}>
        <View style={[styles.impactBar, { width }]} />
      </View>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'timeline':
        return (
          <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
            {waypoints.map((waypoint) => (
              <View
                key={waypoint.id}
                style={[
                  styles.waypointCard,
                  waypoint.isMilestone && styles.milestoneCard
                ]}
              >
                <View style={styles.waypointHeader}>
                  <View style={styles.waypointHeaderLeft}>
                    <Text style={styles.waypointDate}>
                      {formatDate(waypoint.date).toUpperCase()}
                    </Text>
                    {waypoint.isMilestone && (
                      <View style={styles.milestoneButton}>
                        <Text style={styles.milestoneText}>Milestone</Text>
                      </View>
                    )}
                  </View>
                  <TouchableOpacity
                    style={styles.waypointMenuButton}
                    onPress={(event) => {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      handleWaypointMenuPress(waypoint.id, event);
                    }}
                  >
                    <MoreVertical size={16} color={COLORS.gray500} />
                  </TouchableOpacity>
                </View>
                
                <Text style={styles.waypointContent}>
                  {waypoint.content}
                </Text>
                
                <View style={styles.waypointMeta}>
                  <View style={styles.sentimentContainer}>
                    {getSentimentBolts(waypoint.sentiment)}
                  </View>
                  
                  <View style={styles.impactContainer}>
                    <Text style={styles.impactLabel}>–</Text>
                    {getImpactBar(waypoint.impact)}
                    <Text style={styles.impactLabel}>+</Text>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>
        );
        
      case 'path':
        return (
          <View style={styles.pathContainer}>
            <View style={styles.pathVisualization}>
              <View style={styles.timelineBar}>
                {['Jul 2024', 'Aug 2024', 'Sep 2024', 'Oct 2024', 'Nov 2024'].map((month, index) => (
                  <Text key={index} style={styles.timelineLabel}>{month}</Text>
                ))}
              </View>
              
              <View style={styles.pathArea}>
                {/* Simplified path visualization */}
                {waypoints.slice(0, 8).map((waypoint, index) => {
                  const x = (index / 7) * 100;
                  const y = 50 + (waypoint.sentiment / 200) * 40; // Convert sentiment to y position
                  return (
                    <View
                      key={waypoint.id}
                      style={[
                        styles.pathPoint,
                        {
                          left: `${x}%`,
                          top: `${y}%`,
                          backgroundColor: waypoint.isMilestone ? '#FFD700' : journey.color
                        },
                        waypoint.isMilestone && styles.milestonePoint
                      ]}
                    />
                  );
                })}
              </View>
            </View>
            
            <View style={styles.playbackControls}>
              <View style={styles.progressBar}>
                <View style={[styles.progress, { backgroundColor: journey.color }]} />
              </View>
              
              <View style={styles.controls}>
                <TouchableOpacity style={styles.controlButton}>
                  <SkipBack size={20} color={COLORS.gray700} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton}>
                  <ChevronLeft size={20} color={COLORS.gray700} />
                </TouchableOpacity>
                <TouchableOpacity style={[styles.playButton, { backgroundColor: journey.color }]}>
                  <Play size={20} color="#FFFFFF" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton}>
                  <ChevronRight size={20} color={COLORS.gray700} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton}>
                  <SkipForward size={20} color={COLORS.gray700} />
                </TouchableOpacity>
              </View>
            </View>
            
            {waypoints[0] && (
              <View style={styles.pathWaypointCard}>
                <Text style={styles.pathWaypointDate}>
                  {formatDate(waypoints[0].date)}
                </Text>
                <Text style={styles.pathWaypointTitle}>
                  {waypoints[0].content.length > 50
                    ? waypoints[0].content.substring(0, 50) + '...'
                    : waypoints[0].content}
                </Text>
                <TouchableOpacity style={styles.pathWaypointButton}>
                  <MoreHorizontal size={16} color={COLORS.gray500} />
                </TouchableOpacity>
              </View>
            )}
          </View>
        );
        
      case 'insights':
        const positiveWaypoints = waypoints.filter(w => w.sentiment > 20).length;
        const milestones = waypoints.filter(w => w.isMilestone).length;
        const positivePercentage = waypoints.length > 0 ? Math.round((positiveWaypoints / waypoints.length) * 100) : 0;
        const daysActive = waypoints.length > 0 ? Math.ceil((Date.now() - new Date(waypoints[waypoints.length - 1].date).getTime()) / (1000 * 60 * 60 * 24)) : 0;
        
        return (
          <ScrollView style={styles.insightsContent} showsVerticalScrollIndicator={false}>
            <View style={styles.metricsGrid}>
              <View style={styles.metricCard}>
                <Text style={styles.metricNumber}>{waypoints.length}</Text>
                <Text style={styles.metricLabel}>Waypoints</Text>
              </View>
              
              <View style={styles.metricCard}>
                <View style={styles.metricHeader}>
                  <Text style={styles.metricNumber}>{milestones}</Text>
                  <Star size={16} color="#FFD700" fill="#FFD700" />
                </View>
                <Text style={styles.metricLabel}>Milestones</Text>
              </View>
              
              <View style={styles.metricCard}>
                <Text style={[styles.metricNumber, { color: COLORS.primary }]}>
                  {positivePercentage}%
                </Text>
                <Text style={styles.metricLabel}>Positive</Text>
              </View>
              
              <View style={styles.metricCard}>
                <Text style={styles.metricNumber}>{daysActive}</Text>
                <Text style={styles.metricLabel}>Days Active</Text>
              </View>
            </View>
            
            <View style={styles.chartSection}>
              <Text style={styles.chartTitle}>Sentiment Distribution</Text>
              <View style={styles.donutChart}>
                <Text style={[styles.donutPercentage, { color: COLORS.primary }]}>
                  {positivePercentage}%
                </Text>
                <Text style={styles.donutLabel}>Positive</Text>
              </View>
              
              <View style={styles.chartLegend}>
                <View style={styles.legendItem}>
                  <View style={[styles.legendDot, { backgroundColor: COLORS.primary }]} />
                  <Text style={styles.legendText}>{positiveWaypoints}</Text>
                  <Text style={styles.legendLabel}>Positive</Text>
                </View>
                <View style={styles.legendItem}>
                  <View style={[styles.legendDot, { backgroundColor: COLORS.gray400 }]} />
                  <Text style={styles.legendText}>{waypoints.length - positiveWaypoints - 1}</Text>
                  <Text style={styles.legendLabel}>Neutral</Text>
                </View>
                <View style={styles.legendItem}>
                  <View style={[styles.legendDot, { backgroundColor: COLORS.error }]} />
                  <Text style={styles.legendText}>1</Text>
                  <Text style={styles.legendLabel}>Challenge</Text>
                </View>
              </View>
            </View>
            
            <Text style={styles.monthlyTitle}>Monthly Progress</Text>
            
            <View style={styles.insightCard}>
              <TrendingUp size={20} color={COLORS.primary} />
              <View style={styles.insightContent}>
                <Text style={styles.insightTitle}>Strong Momentum</Text>
                <Text style={styles.insightText}>
                  You've maintained consistent growth with October being your most productive month. Keep this rhythm!
                </Text>
              </View>
            </View>
          </ScrollView>
        );
        
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={[styles.header, { borderBottomColor: journey.color }]}>
        <View style={styles.headerTop}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <ArrowLeft size={24} color={COLORS.gray700} />
          </TouchableOpacity>
          
          <View style={styles.headerCenter}>
            <View style={styles.journeyInfo}>
              <View style={[styles.colorCircle, { backgroundColor: journey.color }]} />
              <Text style={styles.journeyTitle}>{journey.title}</Text>
            </View>
            <View style={styles.journeyMeta}>
              <Flag size={14} color={COLORS.gray500} />
              <Text style={styles.metaText}>{journey.waypointCount}</Text>
              <Text style={styles.metaText}>Since {formatDate(journey.startDate)}</Text>
            </View>
          </View>
          
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Search size={20} color={COLORS.gray500} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Filter size={20} color={COLORS.gray500} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={(event) => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                handleJourneyMenuPress(event);
              }}
            >
              <MoreVertical size={20} color={COLORS.gray500} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.tabBar}>
          {[
            { key: 'timeline', label: 'Timeline', icon: Flag },
            { key: 'path', label: 'Path', icon: TrendingUp },
            { key: 'insights', label: 'Insights', icon: TrendingUp }
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                activeTab === tab.key && { borderBottomColor: journey.color }
              ]}
              onPress={() => setActiveTab(tab.key as any)}
            >
              <tab.icon
                size={16}
                color={activeTab === tab.key ? journey.color : COLORS.gray500}
              />
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.key && { color: journey.color }
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      <View style={styles.content}>
        {renderTabContent()}
      </View>
      
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: journey.color }]}
        onPress={() => navigation.navigate('AddWaypoint', { journeyId })}
      >
        <Plus size={24} color="#FFFFFF" />
      </TouchableOpacity>

      {/* Journey Dropdown Menu */}
      <DropdownMenu
        visible={showJourneyMenu}
        onClose={() => setShowJourneyMenu(false)}
        options={journeyOptions}
        position={menuPosition}
      />

      {/* Waypoint Dropdown Menu */}
      <DropdownMenu
        visible={!!showWaypointMenu}
        onClose={() => setShowWaypointMenu(null)}
        options={showWaypointMenu ? getWaypointOptions(showWaypointMenu) : []}
        position={menuPosition}
      />

      {/* Delete Journey Confirmation */}
      <ConfirmationModal
        isVisible={showDeleteJourneyModal}
        title="Delete Journey"
        message={`Are you sure you want to delete "${journey.title}"? This will also delete all waypoints and cannot be undone.`}
        confirmText="Delete Journey"
        onConfirm={confirmDeleteJourney}
        onCancel={() => setShowDeleteJourneyModal(false)}
        destructive
      />

      {/* Delete Waypoint Confirmation */}
      <ConfirmationModal
        isVisible={!!showDeleteWaypointModal}
        title="Delete Waypoint"
        message="Are you sure you want to delete this waypoint? This action cannot be undone."
        confirmText="Delete Waypoint"
        onConfirm={() => showDeleteWaypointModal && confirmDeleteWaypoint(showDeleteWaypointModal)}
        onCancel={() => setShowDeleteWaypointModal(null)}
        destructive
      />

      {/* Edit Waypoint Modal */}
      <Modal
        visible={!!editingWaypoint}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.editModalContainer}>
          <View style={styles.editModalHeader}>
            <Pressable 
              style={styles.closeButton}
              onPress={() => setEditingWaypoint(null)}
            >
              <X size={24} color={COLORS.gray700} />
            </Pressable>
            <Text style={styles.editModalTitle}>Edit Waypoint</Text>
            <View style={styles.placeholder} />
          </View>
          
          {editingWaypoint && (
            <WaypointForm
              initialData={waypoints.find(w => w.id === editingWaypoint)}
              onSubmit={handleUpdateWaypoint}
              buttonText="Save Changes"
              journeyColor={journey.color}
            />
          )}
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background
  },
  header: {
    backgroundColor: COLORS.background,
    paddingTop: 60,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 16
  },
  backButton: {
    padding: 8,
    marginRight: 8
  },
  headerCenter: {
    flex: 1
  },
  journeyInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4
  },
  colorCircle: {
    width: 12,
    height: 12,
    borderRadius: 6
  },
  journeyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.gray900
  },
  journeyMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  metaText: {
    fontSize: 12,
    color: COLORS.gray500
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8
  },
  actionButton: {
    padding: 8
  },
  tabBar: {
    flexDirection: 'row'
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    paddingVertical: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent'
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.gray500
  },
  content: {
    flex: 1
  },
  tabContent: {
    flex: 1,
    padding: 16
  },
  waypointCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLORS.gray200
  },
  milestoneCard: {
    backgroundColor: '#E3F2FD',
    borderColor: COLORS.primary
  },
  waypointHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  waypointHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  waypointMenuButton: {
    padding: 4
  },
  waypointDate: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.primary,
    letterSpacing: 0.5
  },
  milestoneButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12
  },
  milestoneText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500'
  },
  waypointContent: {
    fontSize: 14,
    color: COLORS.gray900,
    lineHeight: 20,
    marginBottom: 12
  },
  waypointMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  sentimentContainer: {
    flexDirection: 'row',
    gap: 2
  },
  impactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  impactLabel: {
    fontSize: 12,
    color: COLORS.gray400
  },
  impactBarContainer: {
    width: 60,
    height: 4,
    backgroundColor: COLORS.gray200,
    borderRadius: 2
  },
  impactBar: {
    height: '100%',
    backgroundColor: COLORS.primary,
    borderRadius: 2
  },
  pathContainer: {
    flex: 1
  },
  pathVisualization: {
    height: 200,
    margin: 16,
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 16
  },
  timelineBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16
  },
  timelineLabel: {
    fontSize: 10,
    color: COLORS.gray500
  },
  pathArea: {
    flex: 1,
    position: 'relative'
  },
  pathPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4
  },
  milestonePoint: {
    width: 12,
    height: 12,
    borderRadius: 6,
    shadowColor: '#FFD700',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 4
  },
  playbackControls: {
    padding: 16
  },
  progressBar: {
    height: 4,
    backgroundColor: COLORS.gray200,
    borderRadius: 2,
    marginBottom: 16
  },
  progress: {
    width: '30%',
    height: '100%',
    borderRadius: 2
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.gray100,
    justifyContent: 'center',
    alignItems: 'center'
  },
  playButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center'
  },
  pathWaypointCard: {
    margin: 16,
    padding: 16,
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.gray200
  },
  pathWaypointDate: {
    fontSize: 12,
    color: COLORS.gray500,
    marginBottom: 4
  },
  pathWaypointTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.gray900,
    marginBottom: 8
  },
  pathWaypointButton: {
    alignSelf: 'flex-end',
    padding: 4
  },
  insightsContent: {
    flex: 1,
    padding: 16
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24
  },
  metricCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: COLORS.gray200
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  metricNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.gray900
  },
  metricLabel: {
    fontSize: 12,
    color: COLORS.gray500,
    marginTop: 4
  },
  chartSection: {
    marginBottom: 24
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.gray900,
    marginBottom: 16
  },
  donutChart: {
    alignItems: 'center',
    marginBottom: 16
  },
  donutPercentage: {
    fontSize: 32,
    fontWeight: '700'
  },
  donutLabel: {
    fontSize: 12,
    color: COLORS.gray500
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'space-around'
  },
  legendItem: {
    alignItems: 'center',
    gap: 4
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4
  },
  legendText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.gray900
  },
  legendLabel: {
    fontSize: 12,
    color: COLORS.gray500
  },
  monthlyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.gray900,
    marginBottom: 16
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: COLORS.gray200
  },
  insightContent: {
    flex: 1
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.gray900,
    marginBottom: 4
  },
  insightText: {
    fontSize: 12,
    color: COLORS.gray500,
    lineHeight: 16
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8
  },

  editModalContainer: {
    flex: 1,
    backgroundColor: COLORS.background
  },
  editModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200
  },
  closeButton: {
    padding: 8
  },
  editModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.gray900
  },
  placeholder: {
    width: 40
  }
});
