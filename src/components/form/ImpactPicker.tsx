import React from 'react';
import { View, StyleSheet, Pressable } from 'react-native';
import { COLORS } from '../../constants/colors';

interface ImpactPickerProps {
  value: 'low' | 'medium' | 'high';
  onChange: (newValue: 'low' | 'medium' | 'high') => void;
  journeyColor: string;
}

export default function ImpactPicker({ value, onChange, journeyColor }: ImpactPickerProps) {
  const getIconStyle = (level: 'low' | 'medium' | 'high') => {
    const baseStyle = styles.impactOption;
    if (level === 'low') {
      return [baseStyle, { width: 24 }]; // Single circle width
    } else if (level === 'medium') {
      return [baseStyle, { width: 36, marginLeft: 8 }]; // Double circle width + visual gap
    } else {
      return [baseStyle, { width: 48, marginLeft: 8 }]; // Triple circle width + visual gap
    }
  };

  const renderOption = (level: 'low' | 'medium' | 'high') => {
    const isSelected = value === level;
    
    return (
      <View style={getIconStyle(level)}>
        <Pressable
          key={level}
          style={styles.impactTouchable}
          onPress={() => onChange(level)}
          hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
        >
          <View style={styles.impactCircleWrapper}>
          {/* Level 1 (Low Impact) - Single Circle */}
          {level === 'low' && (
            <View style={[
              styles.impactCircle,
              {
                borderColor: COLORS.gray400,
                backgroundColor: isSelected ? journeyColor : 'transparent'
              }
            ]} />
          )}
          
          {/* Level 2 (Medium Impact) - Double Circles */}
          {level === 'medium' && (
            <>
              <View style={[
                styles.impactCircle,
                {
                  borderColor: COLORS.gray400,
                  backgroundColor: isSelected ? journeyColor : 'transparent'
                }
              ]} />
              <View style={[
                styles.impactCircleOuter,
                { borderColor: COLORS.gray400 }
              ]} />
            </>
          )}
          
          {/* Level 3 (High Impact) - Triple Circles */}
          {level === 'high' && (
            <>
              <View style={[
                styles.impactCircle,
                {
                  borderColor: COLORS.gray400,
                  backgroundColor: isSelected ? journeyColor : 'transparent'
                }
              ]} />
              <View style={[
                styles.impactCircleOuter,
                { borderColor: COLORS.gray400 }
              ]} />
              <View style={[
                styles.impactCircleOutermost,
                { borderColor: COLORS.gray400 }
              ]} />
            </>
          )}
        </View>
      </Pressable>
    </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderOption('low')}
      {renderOption('medium')}
      {renderOption('high')}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: 4,
    height: 48
  },
  impactOption: {
    height: 48,
    justifyContent: 'center',
    alignItems: 'center'
  },
  impactTouchable: {
    width: 40,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center'
  },
  impactCircleWrapper: {
    width: 40,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative'
  },
  // Inner circle (consistent across all levels)
  impactCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    position: 'absolute'
  },
  // Middle circle (Level 2 & 3)
  impactCircleOuter: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 2,
    position: 'absolute',
    backgroundColor: 'transparent'
  },
  // Outermost circle (Level 3 only)
  impactCircleOutermost: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 2,
    position: 'absolute',
    backgroundColor: 'transparent'
  }
});