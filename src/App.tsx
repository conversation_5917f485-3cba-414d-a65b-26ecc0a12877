
import React from 'react';
import { DarkTheme, DefaultTheme, NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useColorScheme } from 'react-native';
import { Toaster } from 'sonner-native';
import MainNavigator from './navigation';

export default function App() {
    const colorScheme = useColorScheme();
    const isDark = colorScheme === 'dark';

    // Always extend the base theme from react.navigation.
    // Otherwise, error such as cannot read property 'n.medium' of undefined will occur which basically means the fonts property is missing from the theme.
    const navigationTheme = {
        ...(isDark ? DarkTheme : DefaultTheme),
        colors: {
            // Change this to match the app's theme. Either use Dark or light. Add conditional only when theme switching is required.
            ...DefaultTheme.colors
            // isDark ? DarkTheme.colors : DefaultTheme.colors
        },
    };

    return (
        <SafeAreaProvider>
            <NavigationContainer theme={navigationTheme}>
                <StatusBar
                    style={isDark ? 'light' : 'dark'}
                />
                <Toaster theme={'light'} richColors />
                <MainNavigator />
            </NavigationContainer>
        </SafeAreaProvider>
    );
}
