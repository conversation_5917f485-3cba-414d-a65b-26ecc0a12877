
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants';
import { Journey, mockJourneys } from '../mocks/journeys';
import { useWaypointStore } from './waypointStore';

interface JourneyState {
  journeys: Journey[];
  selectedJourney: Journey | null;
  loading: boolean;
  error: string | null;
}

interface JourneyActions {
  createJourney: (journey: Omit<Journey, 'id' | 'createdAt' | 'updatedAt' | 'waypointCount' | 'milestoneCount'>) => void;
  updateJourney: (id: string, updates: Partial<Journey>) => void;
  deleteJourney: (id: string) => void;
  selectJourney: (id: string) => void;
  getJourneyById: (id: string) => Journey | undefined;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

type JourneyStore = JourneyState & JourneyActions;

export const useJourneyStore = create<JourneyStore>()(persist(
  (set, get) => ({
    // Initial state
    journeys: mockJourneys,
    selectedJourney: null,
    loading: false,
    error: null,

    // Actions
    createJourney: (journeyData) => {
      const newJourney: Journey = {
        ...journeyData,
        id: Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date(),
        waypointCount: 0,
        milestoneCount: 0
      };

      set(state => ({
        journeys: [newJourney, ...state.journeys],
        error: null
      }));
    },

    updateJourney: (id, updates) => {
      set(state => ({
        journeys: state.journeys.map(journey =>
          journey.id === id
            ? { ...journey, ...updates, updatedAt: new Date() }
            : journey
        ),
        selectedJourney: state.selectedJourney?.id === id
          ? { ...state.selectedJourney, ...updates, updatedAt: new Date() }
          : state.selectedJourney,
        error: null
      }));
    },

    deleteJourney: (id) => {
      // First delete all waypoints for this journey
      const waypointStore = useWaypointStore.getState();
      waypointStore.deleteWaypointsForJourney(id);
      
      // Then delete the journey itself
      set(state => ({
        journeys: state.journeys.filter(journey => journey.id !== id),
        selectedJourney: state.selectedJourney?.id === id ? null : state.selectedJourney,
        error: null
      }));
    },

    selectJourney: (id) => {
      const journey = get().journeys.find(j => j.id === id);
      set({ selectedJourney: journey || null });
    },

    getJourneyById: (id) => {
      return get().journeys.find(journey => journey.id === id);
    },

    setLoading: (loading) => set({ loading }),
    
    setError: (error) => set({ error })
  }),
  {
    name: STORAGE_KEYS.JOURNEY_STORE,
    storage: {
      getItem: async (name: string) => {
        const value = await AsyncStorage.getItem(name);
        if (!value) return null;
        
        const parsed = JSON.parse(value);
        
        // Convert date strings back to Date objects
        if (parsed.state) {
          // Handle journeys array
          if (parsed.state.journeys) {
            parsed.state.journeys = parsed.state.journeys.map((journey: any) => ({
              ...journey,
              startDate: journey.startDate ? new Date(journey.startDate) : new Date(),
              createdAt: journey.createdAt ? new Date(journey.createdAt) : new Date(),
              updatedAt: journey.updatedAt ? new Date(journey.updatedAt) : new Date()
            }));
          }
          
          // Handle selectedJourney
          if (parsed.state.selectedJourney) {
            parsed.state.selectedJourney = {
              ...parsed.state.selectedJourney,
              startDate: parsed.state.selectedJourney.startDate ? new Date(parsed.state.selectedJourney.startDate) : new Date(),
              createdAt: parsed.state.selectedJourney.createdAt ? new Date(parsed.state.selectedJourney.createdAt) : new Date(),
              updatedAt: parsed.state.selectedJourney.updatedAt ? new Date(parsed.state.selectedJourney.updatedAt) : new Date()
            };
          }
        }
        
        return parsed;
      },
      setItem: async (name: string, value: any) => {
        await AsyncStorage.setItem(name, JSON.stringify(value));
      },
      removeItem: async (name: string) => {
        await AsyncStorage.removeItem(name);
      }
    },
    partialize: (state) => ({
      journeys: state.journeys,
      selectedJourney: state.selectedJourney
    })
  }
));
