
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants';
import { Waypoint, mockWaypoints } from '../mocks/journeys';

interface WaypointState {
  waypoints: Waypoint[];
  loading: boolean;
  error: string | null;
}

interface WaypointActions {
  createWaypoint: (waypoint: Omit<Waypoint, 'id' | 'createdAt'>) => void;
  updateWaypoint: (id: string, updates: Partial<Waypoint>) => void;
  deleteWaypoint: (id: string) => void;
  deleteWaypointsForJourney: (journeyId: string) => void;
  getWaypointsByJourney: (journeyId: string) => Waypoint[];
  getMilestonesByJourney: (journeyId: string) => Waypoint[];
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

type WaypointStore = WaypointState & WaypointActions;

export const useWaypointStore = create<WaypointStore>()(persist(
  (set, get) => ({
    // Initial state
    waypoints: mockWaypoints,
    loading: false,
    error: null,

    // Actions
    createWaypoint: (waypointData) => {
      const newWaypoint: Waypoint = {
        ...waypointData,
        id: Date.now().toString(),
        createdAt: new Date()
      };

      set(state => ({
        waypoints: [newWaypoint, ...state.waypoints],
        error: null
      }));
    },

    updateWaypoint: (id, updates) => {
      set(state => ({
        waypoints: state.waypoints.map(waypoint =>
          waypoint.id === id
            ? { ...waypoint, ...updates }
            : waypoint
        ),
        error: null
      }));
    },

    deleteWaypoint: (id) => {
      set(state => ({
        waypoints: state.waypoints.filter(waypoint => waypoint.id !== id),
        error: null
      }));
    },

    deleteWaypointsForJourney: (journeyId) => {
      set(state => ({
        waypoints: state.waypoints.filter(waypoint => waypoint.journeyId !== journeyId),
        error: null
      }));
    },

    getWaypointsByJourney: (journeyId) => {
      return get().waypoints
        .filter(waypoint => waypoint.journeyId === journeyId)
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    },

    getMilestonesByJourney: (journeyId) => {
      return get().waypoints
        .filter(waypoint => waypoint.journeyId === journeyId && waypoint.isMilestone)
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    },

    setLoading: (loading) => set({ loading }),
    
    setError: (error) => set({ error })
  }),
  {
    name: STORAGE_KEYS.WAYPOINT_STORE,
    storage: {
      getItem: async (name: string) => {
        const value = await AsyncStorage.getItem(name);
        if (!value) return null;
        
        const parsed = JSON.parse(value);
        
        // Convert date strings back to Date objects
        if (parsed.state && parsed.state.waypoints) {
          parsed.state.waypoints = parsed.state.waypoints.map((waypoint: any) => ({
            ...waypoint,
            date: waypoint.date ? new Date(waypoint.date) : new Date(),
            createdAt: waypoint.createdAt ? new Date(waypoint.createdAt) : new Date()
          }));
        }
        
        return parsed;
      },
      setItem: async (name: string, value: any) => {
        await AsyncStorage.setItem(name, JSON.stringify(value));
      },
      removeItem: async (name: string) => {
        await AsyncStorage.removeItem(name);
      }
    },
    partialize: (state) => ({
      waypoints: state.waypoints
    })
  }
));
