
export interface Journey {
  id: string;
  title: string;
  description?: string;
  color: string;
  startDate: Date;
  createdAt: Date;
  updatedAt: Date;
  waypointCount: number;
  milestoneCount: number;
  status: 'active' | 'done';
}

export interface Waypoint {
  id: string;
  journeyId: string;
  content: string;
  date: Date;
  sentiment: number; // -100 to 100
  impact: 'low' | 'medium' | 'high';
  isMilestone: boolean;
  createdAt: Date;
}

export const mockJourneys: Journey[] = [
  {
    id: '1',
    title: 'Career Transition',
    description: 'Transitioning from marketing to product management',
    color: '#2196F3',
    startDate: new Date(2024, 5, 1),
    createdAt: new Date(2024, 5, 1),
    updatedAt: new Date(2024, 11, 19, 22, 0),
    waypointCount: 12,
    milestoneCount: 3,
    status: 'active'
  },
  {
    id: '2', 
    title: 'Fitness Journey',
    description: 'Getting back in shape and building healthy habits',
    color: '#10B981',
    startDate: new Date(2024, 4, 15),
    createdAt: new Date(2024, 4, 15),
    updatedAt: new Date(2024, 11, 18),
    waypointCount: 28,
    milestoneCount: 5,
    status: 'active'
  },
  {
    id: '3',
    title: 'Learning Spanish',
    description: 'Mastering Spanish for travel and personal growth',
    color: '#8B5CF6',
    startDate: new Date(2024, 3, 10),
    createdAt: new Date(2024, 3, 10),
    updatedAt: new Date(2024, 11, 16),
    waypointCount: 18,
    milestoneCount: 2,
    status: 'done'
  },
  {
    id: '4',
    title: 'Home Renovation',
    description: 'Renovating our kitchen and living room',
    color: '#F97316',
    startDate: new Date(2024, 7, 1),
    createdAt: new Date(2024, 7, 1),
    updatedAt: new Date(2024, 11, 5),
    waypointCount: 7,
    milestoneCount: 1,
    status: 'active'
  }
];

export const mockWaypoints: Waypoint[] = [
  // Career Transition waypoints
  {
    id: 'w1',
    journeyId: '1',
    content: 'Had my first informational interview with a PM at Google. Really insightful conversation about the role and what it takes to succeed.',
    date: new Date(2024, 5, 15),
    sentiment: 75,
    impact: 'high',
    isMilestone: true,
    createdAt: new Date(2024, 5, 15)
  },
  {
    id: 'w2', 
    journeyId: '1',
    content: 'Completed my first product case study analysis. Feeling more confident about my analytical skills.',
    date: new Date(2024, 6, 8),
    sentiment: 60,
    impact: 'medium',
    isMilestone: false,
    createdAt: new Date(2024, 6, 8)
  },
  {
    id: 'w3',
    journeyId: '1',
    content: 'Got rejected from my first PM application. Tough feedback but learned a lot about areas to improve.',
    date: new Date(2024, 7, 22),
    sentiment: -30,
    impact: 'medium',
    isMilestone: false,
    createdAt: new Date(2024, 7, 22)
  }
];
